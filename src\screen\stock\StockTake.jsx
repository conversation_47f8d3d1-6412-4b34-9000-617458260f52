import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TextInput,
    TouchableOpacity,
    ScrollView,
    FlatList,
    Modal
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import Icon from 'react-native-vector-icons/Feather';
import Navbar from '../../components/Navbar';
import CheckBox from '@react-native-community/checkbox';

import { fetchItemList } from '../../apiHandling/StockAPI/fetchItemListAPI';
import { fetchItemDetail, fetchItemStock } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { bearerToken, loginBranchID } from '../../globals';

const ports = [
    { label: 'COM 1', value: 'COM 1' },
    { label: 'COM 2', value: 'COM 2' },
    { label: 'COM 3', value: 'COM 3' },
];

const StockTake = ({ navigation }) => {
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [itemList, setItemList] = useState([]);
    const [modalVisible, setModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);
    const [stockQty, setStockQty] = useState(0);
    const [stockAltQty, setStockAltQty] = useState(0);
    const [batchList, setBatchList] = useState([]);
    const [batchModalVisible, setBatchModalVisible] = useState(false);
    const [batch, setBatch] = useState('');
    const [newNos, setNewNos] = useState('');
    const [newWt, setNewWt] = useState('');
    const [adjustedNos, setAdjustedNos] = useState('');
    const [adjustedWt, setAdjustedWt] = useState('');


    const [port, setPort] = useState(null);
    const [nos, setNos] = useState('');
    const [kgs, setKgs] = useState('');



    // Form input states - Additional Information
    const [remarks, setRemarks] = useState('');


    useEffect(() => {
        // Load item list when screen mounts
        loadItemList();
    }, []);

    const loadItemList = async () => {
        const data = await fetchItemList(bearerToken, loginBranchID);
        setItemList(data);
    };

    const handleSelectPress = () => {
        // Reset all item-related state
        setItemName('');
        setItemId('');
        setBatch('');
        setBatchList([]);
        setStockQty(0);
        setStockAltQty(0);
        setNos('');
        setKgs('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Open item selection modal
        setModalVisible(true);
    };


    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);

        const detail = await fetchItemDetail(bearerToken, item.ItemID);

        if (!detail) {
            // console.log("Item detail is undefined. Defaulting to Nos enabled and 0.");

            // Default assumption: batch is disabled, sell by weight is false, alt qty is false
            setBatchEnabled(false);
            setSellByWeight(false);
            setAltQtyEnabled(false);

            // Set stock and fields to 0
            setStockQty(0);
            setStockAltQty(0);
            setNos('0');
            setKgs('0');
            setBatchList([]);
            setBatch('');
            return;
        }

        const {
            BatchEnabled,
            SellByWeight,
            AltQtyEnabled,
        } = detail;

        //console.log("Item Detail Flags:", { BatchEnabled, SellByWeight, AltQtyEnabled });

        setBatchEnabled(BatchEnabled);
        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);

        const stockData = await fetchItemStock(bearerToken, loginBranchID, item.ItemID);
        //console.log("Stock Data:", stockData);

        if (BatchEnabled) {
            //console.log("Batch is enabled. Setting batch list and resetting fields.");
            setBatchList(stockData);
            setBatch('');
            setStockQty(0);
            setStockAltQty(0);

        } else if (Array.isArray(stockData) && stockData.length > 0) {
            const stock = stockData[0];
            const qty = stock.StockQty || 0;
            const altQty = stock.StockAltQty || 0;

            setStockQty(qty);
            setStockAltQty(altQty);

            //console.log("Setting stock values from stockData:", { qty, altQty });

            if (SellByWeight && AltQtyEnabled) {
                setKgs(qty.toString());
                setNos(altQty.toString());
            } else if (SellByWeight) {
                setKgs(qty.toString());

            } else {
                setNos(qty.toString());

            }
        } else {
            // Empty or invalid stockData
            // console.log("Empty or invalid stockData. Setting all to 0.");

            setStockQty(0);
            setStockAltQty(0);

            if (SellByWeight && AltQtyEnabled) {
                setKgs('0');
                setNos('0');
            } else if (SellByWeight) {
                setKgs('0');

            } else {
                setNos('0');

            }
        }
    };

    useEffect(() => {
        // Adjusted Nos = New Nos - Available Nos
        const availableNos = parseFloat(nos) || 0;
        const enteredNos = parseFloat(newNos) || 0;
        const adjusted = enteredNos - availableNos;
        setAdjustedNos(adjusted.toString());
    }, [newNos, nos]);

    useEffect(() => {
        // Adjusted Wt = New Wt - Available Kgs
        const availableKgs = parseFloat(kgs) || 0;
        const enteredKgs = parseFloat(newWt) || 0;
        const adjusted = enteredKgs - availableKgs;
        setAdjustedWt(adjusted.toString());
    }, [newWt, kgs]);






    // Edit mode states
    const [editingItemId, setEditingItemId] = useState(null);
    const [isEditing, setIsEditing] = useState(false);



    return (
        <View style={styles.container}>
            <Navbar />
            <ScrollView contentContainerStyle={styles.scrollContent}>
                {/* Header */}
                <View style={styles.headerRow}>
                    <TouchableOpacity onPress={() => navigation.goBack()}>
                        <Icon name="arrow-left" size={20} color="#000" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>STOCK TAKE</Text>
                    <View style={styles.actionButtons}>
                        <TouchableOpacity style={styles.newBtn}>
                            <Icon name="user-plus" size={16} color="#000" />
                            <Text style={styles.actionText}> New</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.viewBtn}>
                            <Icon name="eye" size={16} color="#000" />
                            <Text style={styles.actionText}> View</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.saveBtn}>
                            <Text style={styles.saveText}>Save</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.cancelBtn}>
                            <Text style={styles.cancelText}>Cancel</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Item Details */}
                <View style={styles.section}>
                    <Text style={styles.sectionTitle}>Item Details</Text>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="Item Name"
                            style={styles.inputBox}
                            value={itemName}
                            onChangeText={setItemName}
                        />
                        <TouchableOpacity style={styles.selectBtn} onPress={handleSelectPress}>
                            <Text style={styles.btnTextWhite}>Select</Text>
                        </TouchableOpacity>


                        <Modal visible={modalVisible} transparent animationType="slide">
                            <View
                                style={{
                                    flex: 1,
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                                }}
                            >
                                <View
                                    style={{
                                        width: '90%',
                                        height: '50%',
                                        backgroundColor: '#fff',
                                        borderRadius: 10,
                                        padding: 16,
                                    }}
                                >
                                    {/* Search input aligned to top-right */}
                                    <View
                                        style={{
                                            flexDirection: 'row',
                                            justifyContent: 'flex-end',
                                            marginBottom: 8,
                                        }}
                                    >
                                        <TextInput
                                            placeholder="Search by name or ID"
                                            value={searchText}
                                            onChangeText={setSearchText}
                                            style={{
                                                borderWidth: 1,
                                                borderColor: '#ccc',
                                                borderRadius: 8,
                                                paddingHorizontal: 10,
                                                paddingVertical: 6,
                                                width: 200,
                                                fontSize: 14,
                                            }}
                                        />
                                    </View>

                                    {/* Filtered FlatList */}
                                    <FlatList
                                        data={itemList.filter(
                                            (item) =>
                                                item.ItemName.toLowerCase().includes(searchText.toLowerCase()) ||
                                                item.ItemID.toLowerCase().includes(searchText.toLowerCase())
                                        )}
                                        keyExtractor={(item) => item.ItemID}
                                        numColumns={3}
                                        showsVerticalScrollIndicator={false}
                                        contentContainerStyle={{ paddingBottom: 16 }}
                                        renderItem={({ item }) => (
                                            <TouchableOpacity
                                                style={{
                                                    flex: 1,
                                                    margin: 4,
                                                    padding: 12,
                                                    backgroundColor: '#f0f0f0',
                                                    borderRadius: 6,
                                                    alignItems: 'center',
                                                }}
                                                onPress={() => handleItemSelect(item)}
                                            >
                                                <Text style={{ textAlign: 'center' }}>{item.ItemName}</Text>
                                            </TouchableOpacity>
                                        )}
                                    />

                                    {/* Close button */}
                                    <TouchableOpacity
                                        style={{
                                            backgroundColor: 'red',
                                            padding: 12,
                                            marginTop: 12,
                                            borderRadius: 8,
                                            alignItems: 'center',
                                        }}
                                        onPress={() => setModalVisible(false)}
                                    >
                                        <Text
                                            style={{
                                                color: 'white',
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            Close
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </Modal>

                        {batchEnabled && (
                            <>
                                <TextInput
                                    placeholder="Choose Batch"
                                    style={styles.inputBox}
                                    value={batch}
                                    editable={false}
                                />
                                <TouchableOpacity style={styles.selectBtn} onPress={() => setBatchModalVisible(true)}>
                                    <Text style={styles.btnTextWhite}>Select</Text>
                                </TouchableOpacity>

                                <Modal visible={batchModalVisible} transparent animationType="slide">
                                    <View style={{
                                        flex: 1,
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        backgroundColor: 'rgba(0,0,0,0.3)'
                                    }}>
                                        <View style={{
                                            width: '90%',
                                            height: '50%',
                                            backgroundColor: '#fff',
                                            borderRadius: 10,
                                            padding: 16,
                                        }}>
                                            <FlatList
                                                data={batchList}
                                                keyExtractor={(item) => item.BatchNumber}
                                                renderItem={({ item }) => (
                                                    <TouchableOpacity
                                                        style={{
                                                            padding: 10,
                                                            marginBottom: 8,
                                                            backgroundColor: '#eee',
                                                            borderRadius: 6,
                                                        }}
                                                        onPress={() => {
                                                            setBatch(item.BatchNumber);
                                                            setStockQty(item.StockQty || 0);
                                                            setStockAltQty(item.StockAltQty || 0);

                                                            const qty = item.StockQty || 0;
                                                            const altQty = item.StockAltQty || 0;

                                                            // Auto-fill based on flags
                                                            if (sellByWeight && altQtyEnabled) {
                                                                setKgs(qty.toString());
                                                                setNos(altQty.toString());
                                                            } else if (sellByWeight) {
                                                                setKgs(qty.toString());
                                                                setNos('');
                                                            } else {
                                                                setNos(qty.toString());
                                                                setKgs('');
                                                            }

                                                            setBatchModalVisible(false);
                                                        }}

                                                    >
                                                        <Text>{item.BatchNumber} - Stock: {item.StockQty}</Text>
                                                    </TouchableOpacity>
                                                )}
                                            />
                                            <TouchableOpacity
                                                style={{
                                                    marginTop: 10,
                                                    backgroundColor: 'red',
                                                    padding: 10,
                                                    borderRadius: 8,
                                                    alignItems: 'center'
                                                }}
                                                onPress={() => setBatchModalVisible(false)}
                                            >
                                                <Text style={{ color: 'white' }}>Close</Text>
                                            </TouchableOpacity>
                                        </View>
                                    </View>
                                </Modal>
                            </>
                        )}


                        <View style={styles.dropdownWrapperPort}>
                            <Dropdown
                                style={styles.dropdownPort}
                                data={ports}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Port"
                                value={port}
                                onChange={item => setPort(item.value)}
                                selectedTextStyle={styles.selectedTextStyle}
                                placeholderStyle={styles.placeholderStyle}
                            />
                        </View>
                    </View>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="New Nos"
                            style={[
                                styles.inputSmall,
                                (!sellByWeight && !altQtyEnabled) ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={newNos}
                            onChangeText={setNewNos}
                            editable={!sellByWeight && !altQtyEnabled}
                        />
                        <TextInput
                            placeholder="New Wt"
                            style={[
                                styles.inputSmall,
                                sellByWeight ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={newWt}
                            onChangeText={setNewWt}
                            editable={sellByWeight}
                        />
                        <TextInput
                            placeholder="Nos"
                            style={[
                                styles.inputSmall,
                                (!sellByWeight && !altQtyEnabled) ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={nos}
                            editable={false}
                        />
                        <TextInput
                            placeholder="Wt(Kg)"
                            style={[
                                styles.inputSmall,
                                sellByWeight ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={kgs}
                            editable={false}
                        />
                    </View>

                    <View style={styles.inputRow}>
                        <TextInput
                            placeholder="Adjusted Nos"
                            style={[
                                styles.inputSmall,
                                (!sellByWeight && !altQtyEnabled) ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={adjustedNos}
                            editable={false}
                        />
                        <TextInput
                            placeholder="Adjusted Wt"
                            style={[
                                styles.inputSmall,
                                sellByWeight ? {} : { backgroundColor: '#e0e0e0' }
                            ]}
                            keyboardType="numeric"
                            value={adjustedWt}
                            editable={false}
                        />
                    </View>


                    <View style={styles.row}>
                        <TextInput
                            placeholder="Remarks"
                            style={styles.remarkInput}
                            value={remarks}
                            onChangeText={setRemarks}
                        />
                        <TouchableOpacity
                            style={isEditing ? styles.updateBtn : styles.addBtn}
                        >
                            <Text style={styles.btnText}>{isEditing ? 'Update' : 'Add'}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.clearBtn}>
                            <Text style={styles.btnText}>Clear</Text>
                        </TouchableOpacity>
                    </View>
                </View>

                {/* Table Section */}
                <View style={styles.section}>
                    <View style={styles.tableContainer}>
                        <View style={styles.tableHeader}>
                            <Text style={[styles.headerCell, styles.checkboxCell]}>Select</Text>
                            <Text style={[styles.headerCell, styles.lineNumberCell]}>Line No.</Text>
                            <Text style={[styles.headerCell, styles.itemNameCell]}>Item Name</Text>
                            <Text style={[styles.headerCell, styles.nosCell]}>Nos</Text>
                            <Text style={[styles.headerCell, styles.kgsCell]}>Kgs</Text>
                            <Text style={[styles.headerCell, styles.remarksCell]}>Remarks</Text>
                            <Text style={[styles.headerCell, styles.actionCell]}>Action</Text>
                        </View>
                        {/* 
            <View style={styles.tableScrollContainer}>
              <ScrollView>
                {items.length === 0 ? (
                  <View style={[styles.tableRow, { justifyContent: 'center' }]}>
                    <Text style={{ color: '#999', paddingVertical: 20 }}>No items added yet</Text>
                  </View>
                ) : (
                  items.map((item, index) => (
                    <View key={item.id} style={styles.tableRow}>
                      <View style={styles.checkboxCell}>
                        <CheckBox
                          value={item.selected || false}
                          onValueChange={() => toggleItemSelection(item.id)}
                        />
                      </View>
                      <Text style={[styles.cell, styles.lineNumberCell]}>{index + 1}</Text>
                      <Text style={[styles.cell, styles.itemNameCell]}>{item.itemName}</Text>
                      <Text style={[styles.cell, styles.nosCell]}>{item.nos}</Text>
                      <Text style={[styles.cell, styles.kgsCell]}>{item.kgs}</Text>
                      <Text style={[styles.cell, styles.remarksCell]}>{item.remarks}</Text>
                      <TouchableOpacity
                        style={styles.editBtn}
                        onPress={() => handleEditItem(item)}
                      >
                        <Text style={styles.editBtnText}>Edit</Text>
                      </TouchableOpacity>
                    </View>
                  ))
                )}
              </ScrollView>
            </View> */}
                        <View style={styles.tableScrollContainer}>
                            <ScrollView>
                                {/* Static Row 1 */}
                                <View style={[styles.tableRow, styles.tableRowEven]}>
                                    <View style={[styles.cell, styles.checkboxCell]}>
                                        <CheckBox
                                            value={false}
                                            tintColors={{ true: '#002b5c', false: '#002b5c' }}
                                            style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
                                        />
                                    </View>
                                    <Text style={[styles.cell, styles.lineNumberCell]}>1</Text>
                                    <Text style={[styles.cell, styles.itemNameCell]}>Sample Item 1</Text>
                                    <Text style={[styles.cell, styles.nosCell]}>10</Text>
                                    <Text style={[styles.cell, styles.kgsCell]}>5.5</Text>
                                    <Text style={[styles.cell, styles.remarksCell]}>Good condition</Text>
                                    <View style={[styles.cell, styles.actionCell]}>
                                        <TouchableOpacity>
                                            <Icon name="edit" size={30} color="green" />
                                        </TouchableOpacity>
                                    </View>
                                </View>

                                {/* Static Row 2 */}
                                <View style={styles.tableRow}>
                                    <View style={[styles.cell, styles.checkboxCell]}>
                                        <CheckBox
                                            value={false}
                                            tintColors={{ true: '#002b5c', false: '#002b5c' }}
                                            style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
                                        />
                                    </View>
                                    <Text style={[styles.cell, styles.lineNumberCell]}>2</Text>
                                    <Text style={[styles.cell, styles.itemNameCell]}>Sample Item 2</Text>
                                    <Text style={[styles.cell, styles.nosCell]}>25</Text>
                                    <Text style={[styles.cell, styles.kgsCell]}>12.3</Text>
                                    <Text style={[styles.cell, styles.remarksCell]}>Needs inspection</Text>
                                    <View style={[styles.cell, styles.actionCell]}>
                                        <TouchableOpacity>
                                            <Icon name="edit" size={30} color="green" />
                                        </TouchableOpacity>
                                    </View>
                                </View>

                                {/* Static Row 3 */}
                                <View style={[styles.tableRow, styles.tableRowEven]}>
                                    <View style={[styles.cell, styles.checkboxCell]}>
                                        <CheckBox
                                            value={true}
                                            tintColors={{ true: '#002b5c', false: '#002b5c' }}
                                            style={{ transform: [{ scaleX: 1.4 }, { scaleY: 1.4 }] }}
                                        />
                                    </View>
                                    <Text style={[styles.cell, styles.lineNumberCell]}>3</Text>
                                    <Text style={[styles.cell, styles.itemNameCell]}>Sample Item 3</Text>
                                    <Text style={[styles.cell, styles.nosCell]}>8</Text>
                                    <Text style={[styles.cell, styles.kgsCell]}>3.2</Text>
                                    <Text style={[styles.cell, styles.remarksCell]}>Ready for shipment</Text>
                                    <View style={[styles.cell, styles.actionCell]}>
                                        <TouchableOpacity>
                                            <Icon name="edit" size={30} color="green" />
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </ScrollView>
                        </View>

                        {/* Table Footer */}
                        <View style={styles.footerRow}>
                            {/* Delete Button */}
                            <TouchableOpacity style={styles.deleteBtn}>
                                <Text style={styles.footerText}>Delete Selected Row</Text>
                            </TouchableOpacity>

                        </View>
                    </View>
                    {/* 
          <TouchableOpacity style={styles.deleteBtn} onPress={deleteSelectedItems}>
            <Text style={styles.deleteBtnText}>Delete Selected</Text>
          </TouchableOpacity> */}
                </View>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#e9e9e9',

    },
    scrollContent: {
        paddingBottom: 20,
    },

    headerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        backgroundColor: '#e9e9e9',
        padding: 20,
    },

    headerTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        flex: 1,
        marginLeft: 10,
    },
    actionButtons: {
        flexDirection: 'row',
        gap: 5,
        flexWrap: 'wrap',
    },

    newBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    viewBtn: {
        backgroundColor: '#E2E3E5',
        paddingVertical: 14,
        paddingHorizontal: 25,
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 8,
    },
    saveBtn: {
        backgroundColor: '#28A745',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },

    cancelBtn: {
        backgroundColor: 'red',
        paddingVertical: 14,
        paddingHorizontal: 25,
        borderRadius: 8,
    },

    actionText: {
        fontWeight: 'bold',
        fontSize: 16,
    },
    saveText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    cancelText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },


    section: {
        backgroundColor: '#e9e9e9',
        paddingHorizontal: 10,
        marginBottom: 0,

    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 10,
        marginTop: 0, // ↓ Ensures no extra space on top
        color: '#000',
    },

    inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 8,
        marginBottom: 12,
        width: '100%',
    },

    inputBox: {
        flex: 1,
        borderWidth: 1,
        borderColor: '#999',
        borderRadius: 6,
        paddingHorizontal: 10,
        height: 50, // Increased from 40
        backgroundColor: '#fff',
    },

    dropdownBox: {
        flex: 1,
        minWidth: '30%',
        borderWidth: 1,
        borderColor: '#aaa',
        borderRadius: 6,
        paddingHorizontal: 10,
        height: 50, // Increased from 40
        backgroundColor: '#fff',
    },

    dropdownWrapper: {
        flex: 1,
        minWidth: '30%',
    },

    dropdownWrapperPort: {
        width: 120,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        backgroundColor: '#fff',
    },


    dropdown: {
        height: 50, // Increased from 40
        borderColor: '#aaa',
        borderWidth: 1,
        borderRadius: 6,
        paddingHorizontal: 8,
        backgroundColor: '#fff',
    },
    dropdownPort: {
        height: 50, // Increased from 40
        paddingHorizontal: 8,
    },
    selectedTextStyle: {
        fontSize: 14,
    },
    placeholderStyle: {
        fontSize: 14,
        color: '#888',
    },
    selectBtn: {
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        backgroundColor: '#1E2D52',
        borderRadius: 6,
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
    },
    transitBtn: {
        backgroundColor: '#1E2D52',
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        borderRadius: 6,
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
    },
    newBtnSmall: {
        backgroundColor: '#28A745',
        height: 50, // Increased from 40
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 12,
        borderRadius: 6,
        width: 70,
    },

    remarkInput: {
        height: 50,
        backgroundColor: '#fff',
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        paddingHorizontal: 10,
        marginRight: 8,
    },

    remarkInputlocation: {
        height: 50,
        backgroundColor: '#fff',
        flex: 1,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 5,
        paddingHorizontal: 10,
    },


    addBtn: {
        backgroundColor: 'green',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,             // gap between Add and Clear
    },

    clearBtn: {
        backgroundColor: 'red',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
    },

    btnText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: 'center',
    },
    LocationclearBtn: {
        backgroundColor: 'red',
        paddingHorizontal: 12,
        paddingVertical: 12, // Increased
        height: 50, // Increased from 40
        borderRadius: 6,
        width: 70,
        justifyContent: 'center',
        alignItems: 'center',
    },

    inputSmall: {
        flex: 1,
        minWidth: '22%',
        height: 50, // Increased from 40
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 6,
        paddingHorizontal: 8,
        backgroundColor: '#fff',
    },
    btnText: {
        color: '#fff',
        fontWeight: 'bold',
        textAlign: 'center',
        fontSize: 14, // Added for better visibility
    },
    btnTextWhite: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 14, // Added for better visibility
    },
    inputWithButton: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
        minWidth: '48%',
        gap: 8,
    },
    row: {
        flexDirection: 'row',
        justifyContent: 'space-between',

    },


    tableScrollContainer: {
        maxHeight: 500, // Adjust based on your row height. ~50px * 4 rows
        borderWidth: 1,
        borderColor: '#ccc',

    },

    tableContainer: {
        marginTop: 10,
        overflow: 'hidden',
        borderRadius: 5,
        marginBottom: 10,

    },
    tableHeader: {
        flexDirection: 'row',
        backgroundColor: '#002b5c',
        paddingVertical: 14,
        paddingHorizontal: 8,
        alignItems: 'center',
    },
    headerCell: {
        flex: 1,
        fontWeight: '600',
        color: 'white',
        fontSize: 14,
        textAlign: 'center',
    },
    tableRow: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderColor: '#f0f0f0',
        alignItems: 'center',
        backgroundColor: '#fff',
    },
    tableRowEven: {
        backgroundColor: '#f9f9f9',
    },
    cell: {
        flex: 1,
        paddingHorizontal: 8,
        textAlign: 'center',
        fontSize: 13,
        color: '#333',
    },
    checkboxCell: {
        flex: 0.4,
        textAlign: 'center',
    },
    lineNumberCell: {
        flex: 0.7,
    },
    itemNameCell: {
        flex: 1.5,
        textAlign: 'left',
    },
    nosCell: {
        flex: 0.8,
    },
    kgsCell: {
        flex: 0.8,
    },
    remarksCell: {
        flex: 1.2,
        textAlign: 'left',
    },


    footerRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 10,
        backgroundColor: '#fff',
        borderColor: '#ccc',
        flexWrap: 'wrap',
    },

    footerBox: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#dcdcdc',
        paddingHorizontal: 10,
        paddingVertical: 8,
        borderRadius: 6,
        height: 40,
        marginLeft: 8,
    },

    footerBoxHighlight: {
        backgroundColor: '#bcd4ff',
    },

    footerLabel: {
        color: '#333',
        fontWeight: '600',
    },

    footerLabelHighlight: {
        color: '#003f8a',
    },

    footerValue: {
        color: '#000',
        fontWeight: '600',
        marginLeft: 4,
    },

    deleteBtn: {
        backgroundColor: 'red',
        width: 200,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,
    },

    footerText: {
        color: '#fff',
        fontWeight: 'bold',
    },

    updateBtn: {
        backgroundColor: '#ffa500',
        width: 90,
        height: 50,
        justifyContent: 'center',    // center vertically
        alignItems: 'center',        // center horizontally
        borderRadius: 5,
        marginRight: 10,
    },
    actionCell: {
        width: 50,
        justifyContent: 'center',
        alignItems: 'center',
    },


});

export default StockTake;
